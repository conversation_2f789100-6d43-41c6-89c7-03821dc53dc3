<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $config = config('upload');

    return [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'type' => 'required|string|in:ppt,pptx,doc,docx,pdf',
        'is_active' => 'sometimes|boolean',
        'file' => 'required|file|mimes:ppt,pptx,doc,docx,pdf|max:51200', // 50MB
        'demo_video' => 'nullable|file|mimes:mp4,mov,avi|max:102400', // 100MB
    ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'file.required' => 'Template file is required.',
            'file.mimes' => 'Template file must be a PowerPoint, Word, or PDF file.',
            'file.max' => 'Template file size must not exceed 200MB.',
            'demo_video.mimes' => 'Demo video must be an MP4, AVI, or MOV file.',
            'demo_video.max' => 'Demo video size must not exceed 200MB.',
            'type.in' => 'Template type must be ppt, docx, or pdf.',
        ];
    }
}

